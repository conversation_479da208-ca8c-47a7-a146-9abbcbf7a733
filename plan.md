

### **`plan.md` (Versão 12.0 - Refatoração Completa do Módulo de Recargas)**

**Objetivo:** Implementar um fluxo de venda robusto que não dependa de transações, aprimorar a clareza do recibo de venda, criar um histórico funcional e otimizar a tela de estoque.

-----

### **FASE 1: Implementação da Venda Atômica (Sem Transações Nativas)**

**Meta:** Refatorar o fluxo de venda para ser "tudo ou nada", garantindo que a venda de um combo seja atômica, mesmo sem o uso de transações nativas do MongoDB.

  * **TASK-BE-1.1: Adicionar Métodos de Controle no `RechargeCodeRepository`**

      * **Arquivo:** `infocell-server/src/repositories/recharge_code_repository.rs`.

      * **Ação:** Adicionar duas novas funções ao repositório:

        1.  **Rev<PERSON><PERSON> (`unmark_as_sold`):** Para "devolver" um código ao estoque caso a venda falhe no meio do caminho.
        2.  **Atualização Final (`update_codes_with_sale_id`):** Para vincular os códigos à venda depois que ela for criada com sucesso.

        <!-- end list -->

        ```rust
        // Adicionar estas duas novas funções ao `impl RechargeCodeRepository`

        /// Reverte um código para o estado 'disponível', removendo a referência da venda.
        pub async fn unmark_as_sold(&self, code_id: &ObjectId) -> Result<()> {
            let filter = doc! { "_id": code_id };
            let update = doc! {
                "$set": { "is_available": true },
                "$unset": { "sale_id": "", "sold_at": "" }
            };
            self.collection.update_one(filter, update).await?;
            Ok(())
        }

        /// Atualiza uma lista de códigos com o ID da venda final.
        pub async fn update_codes_with_sale_id(&self, code_ids: &[ObjectId], sale_id: &ObjectId) -> Result<()> {
            let filter = doc! { "_id": { "$in": code_ids } };
            let update = doc! { "$set": { "sale_id": sale_id } };
            self.collection.update_many(filter, update).await?;
            Ok(())
        }
        ```

  * **TASK-BE-1.2: Implementar Lógica de "Reserva com Reversão" no `RechargeService`**

      * **Arquivo:** `infocell-server/src/services/recharge_service.rs`.

      * **Ação:** Substituir a função `perform_recharge_sale` pela nova lógica que não usa transações.

        ```rust
        // Substitua a função perform_recharge_sale inteira por esta

        pub async fn perform_recharge_sale(
            &self,
            erp_client_id: String,
            erp_employee_id: String,
            package_type: String,
            payment_method: String,
            quantity: u32,
        ) -> Result<(RechargeSale, Vec<RechargeCode>)> {
            let package = self.package_repo.find_by_package_type(&package_type).await?
                .ok_or_else(|| AppError::NotFound(format!("Pacote ativo do tipo '{}' não encontrado.", package_type)))?;
            
            let mut types_to_reserve = Vec::new();
            if package_type == "combo" {
                for _ in 0..quantity {
                    types_to_reserve.push("tv".to_string());
                    types_to_reserve.push("filmes".to_string());
                }
            } else {
                for _ in 0..quantity {
                    types_to_reserve.push(package_type.clone());
                }
            }

            let mut reserved_codes: Vec<RechargeCode> = Vec::new();
            
            // FASE DE RESERVA
            for code_type in &types_to_reserve {
                // Usamos um ID temporário apenas para marcar o código. Ele será sobrescrito.
                let temp_id = ObjectId::new();
                match self.code_repo.find_and_mark_one_as_sold(code_type, &temp_id, None).await {
                    Ok(Some(code)) => {
                        reserved_codes.push(code);
                    }
                    Ok(None) | Err(_) => {
                        // REVERSÃO MANUAL em caso de falha
                        tracing::warn!("Falha ao reservar código do tipo '{}'. Revertendo {} códigos já reservados.", code_type, reserved_codes.len());
                        for code_to_revert in &reserved_codes {
                            if let Some(id) = &code_to_revert.id {
                                self.code_repo.unmark_as_sold(id).await?;
                            }
                        }
                        return Err(AppError::BadRequest(format!("Estoque de '{}' esgotado.", code_type)));
                    }
                }
            }

            // FASE DE CONFIRMAÇÃO
            // Se chegamos aqui, todos os códigos foram reservados com sucesso.
            let total_sale_price = package.sale_price * f64::from(quantity);
            let reserved_code_ids: Vec<ObjectId> = reserved_codes.iter().filter_map(|c| c.id).collect();

            let sale = RechargeSale {
                id: None,
                erp_client_id,
                erp_employee_id,
                package_type,
                quantity,
                recharge_code_ids: reserved_code_ids.clone(),
                total_sale_price,
                payment_method,
                sale_timestamp: Utc::now(),
            };

            // Criar o registro da venda
            let final_sale_id = self.sale_repo.create(&sale, None).await?;
            let mut final_sale = sale;
            final_sale.id = Some(final_sale_id.clone());

            // Atualizar os códigos reservados com o ID da venda final
            self.code_repo.update_codes_with_sale_id(&reserved_code_ids, &final_sale_id).await?;

            Ok((final_sale, reserved_codes))
        }
        ```

  * **Nota sobre Risco Residual:** Esta abordagem é a mais segura sem transações, mas existe um risco mínimo: se o servidor cair *exatamente* durante a fase de reversão manual, um código pode ficar "preso", exigindo correção manual no banco de dados.

-----

### **FASE 2: Melhoria do Recibo de Venda**

*(Esta fase permanece como no plano anterior, agora baseada na venda corrigida)*

  * **TASK-FE-2.1: Refatorar Componente `RechargeReceipt.jsx`**
      * **Arquivo:** `tools_infocell/src/components/custom/RechargeReceipt.jsx`.
      * **Detalhes:** Reestruturar o layout para se assemelhar à imagem, com lógica para agrupar e exibir códigos de combos corretamente.

-----

### **FASE 3: Página de Histórico e Reimpressão de Vendas**

*(Esta fase permanece como no plano anterior)*

  * **TASK-BE-3.1: Aprimorar Endpoint de Histórico de Vendas**

      * **Arquivo:** `infocell-server/src/services/recharge_service.rs`.
      * **Detalhes:** Modificar o endpoint `get_sales_report` para que ele retorne não apenas os dados da venda, mas também os detalhes dos códigos vendidos em cada uma.

  * **TASK-FE-3.1: Transformar Relatórios em Histórico de Vendas**

      * **Arquivo:** `tools_infocell/src/app/dashboard-infocell/recharges/reports/page.jsx`.
      * **Detalhes:** Alterar a tabela para ser expansível, mostrando os códigos de cada venda. Adicionar um botão "Gerar Comprovante".

  * **TASK-FE-3.2: Implementar Modal de Reimpressão de Recibo**

      * **Arquivo:** `tools_infocell/src/app/dashboard-infocell/recharges/reports/page.jsx`.
      * **Detalhes:** Ao clicar em "Gerar Comprovante", abrir um modal que renderiza o componente `RechargeReceipt.jsx` com os dados da venda selecionada.

-----

### **FASE 4: Otimização da Tela de Estoque**

*(Esta fase permanece como no plano anterior)*

  * **TASK-FE-4.1: Adicionar Busca e Ordenação na Tela de Estoque**

      * **Arquivo:** `tools_infocell/src/app/dashboard-infocell/recharges/stock/page.jsx`.
      * **Detalhes:** Adicionar um campo de busca por código e tornar os cabeçalhos da tabela clicáveis para ordenação.

  * **TASK-BE-4.1: Aprimorar Endpoint de Estoque**

      * **Arquivos:** `handlers/recharge_handlers.rs` e `repositories/recharge_code_repository.rs`.
      * **Detalhes:** Adicionar suporte para os novos parâmetros de busca e ordenação na consulta ao MongoDB.