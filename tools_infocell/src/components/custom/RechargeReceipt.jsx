"use client";

import React, { useRef } from 'react';
import { Button } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Check<PERSON>ircle, ArrowLef<PERSON>, Co<PERSON>, Printer, Share2 } from 'lucide-react';
import { toast } from "sonner";
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

const RechargeReceipt = ({ saleReceipt, selectedPackage, selectedClient, selectedEmployee, onNewSale }) => {
    const receiptRef = useRef(null);

    const generateReceiptText = React.useCallback(() => {
        if (!saleReceipt || !saleReceipt.sale || !selectedPackage || !selectedClient || !selectedEmployee) return '';
        const { sale, codes } = saleReceipt;

        let text = `INFOCELL - RECIBO\n`;
        text += `================================\n\n`;
        text += `${new Date(sale.sale_timestamp).toLocaleDateString('pt-BR')}\n`;
        text += `${selectedClient.nome}\n\n`;
        text += `RECARGAS\n`;

        // Agrupar códigos por tipo
        const tvCodes = codes.filter(c => c.package_type === 'tv');
        const filmesCodes = codes.filter(c => c.package_type === 'filmes');

        if (tvCodes.length > 0) {
            text += `TV:\n`;
            tvCodes.forEach(c => { text += `${c.code.replace(/(.{4})/g, '$1 ').trim()}\n`; });
        }
        if (filmesCodes.length > 0) {
            text += `FILMES:\n`;
            filmesCodes.forEach(c => { text += `${c.code.replace(/(.{4})/g, '$1 ').trim()}\n`; });
        }
        if (tvCodes.length === 0 && filmesCodes.length === 0) {
            codes.forEach(c => { text += `${c.code.replace(/(.{4})/g, '$1 ').trim()}\n`; });
        }

        text += `\nValidade: ${selectedPackage.validity_days} dias após recarregar na tv\n\n`;
        text += `R$ ${sale.total_sale_price.toFixed(2)}\n`;
        text += `${sale.payment_method}\n\n`;
        text += `Vendedor: ${selectedEmployee.nome}\n`;

        return text;
    }, [saleReceipt, selectedPackage, selectedClient, selectedEmployee]);

    const handleCopy = () => {
        navigator.clipboard.writeText(generateReceiptText()).then(() => {
            toast.success("Recibo copiado para a área de transferência.");
        }, () => {
            toast.error("Falha ao copiar o recibo.");
        });
    };

    const handlePrint = () => {
        window.print();
    };

    const handleShare = async () => {
        if (!receiptRef.current) return;
        toast.info("Gerando PDF para compartilhamento...");

        try {
            const canvas = await html2canvas(receiptRef.current, { scale: 2 });
            const imgData = canvas.toDataURL('image/png');
            
            const pdf = new jsPDF('p', 'mm', 'a4'); // Formato A4
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = (canvas.height * pdfWidth) / canvas.width;
            
            pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
            const pdfBlob = pdf.output('blob');

            const pdfFile = new File([pdfBlob], 'recibo-infocell.pdf', { type: 'application/pdf' });

            if (navigator.canShare && navigator.canShare({ files: [pdfFile] })) {
                await navigator.share({
                    files: [pdfFile],
                    title: 'Recibo Infocell',
                    text: 'Recibo da sua compra na Infocell.',
                });
                toast.success("Recibo compartilhado!");
            } else {
                 // Fallback para download se o compartilhamento não for suportado
                const link = document.createElement('a');
                link.href = URL.createObjectURL(pdfBlob);
                link.download = 'recibo-infocell.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                toast.success("PDF do recibo baixado!");
            }
        } catch (error) {
            console.error("Erro ao gerar/compartilhar PDF:", error);
            toast.error("Ocorreu um erro ao tentar gerar o PDF.");
        }
    };

    if (!saleReceipt || !saleReceipt.sale) {
        return <p>Carregando recibo...</p>;
    }
    
    const { sale, codes } = saleReceipt;

    return (
        <div className="flex flex-col items-center w-full">
            <style jsx global>{`
                @media print {
                    body * {
                        visibility: hidden;
                    }
                    #printable-receipt, #printable-receipt * {
                        visibility: visible;
                    }
                    #printable-receipt {
                        position: absolute;
                        left: 0;
                        top: 0;
                        margin: 0;
                        padding: 0;
                        width: 100%;
                    }
                }
                @page { 
                    size: a4;
                    margin: 20mm;
                }
            `}</style>
            
            <Card className="w-full max-w-4xl no-print mb-4">
                 <CardHeader className="text-center">
                    <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                    <CardTitle className="mt-4">Venda Realizada com Sucesso!</CardTitle>
                </CardHeader>
            </Card>

            {/* A4 Preview - hidden on main screen, used for PDF generation and print */}
            <div className="absolute top-0 opacity-0 h-0 overflow-hidden -z-10">
                <div id="printable-receipt" ref={receiptRef} className="bg-white text-black p-8 shadow-lg w-[210mm] h-[297mm] relative">
                    {/* Watermark */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-10 pointer-events-none">
                        <div className="text-6xl font-bold text-gray-400 transform -rotate-45">
                            INFOCELL
                        </div>
                    </div>

                    {/* Header - Data e Nome */}
                    <div className="text-center mb-8">
                        <div className="text-lg font-bold mb-2">
                            {new Date(sale.sale_timestamp).toLocaleDateString('pt-BR')}
                        </div>
                        <div className="text-base">
                            {selectedClient?.nome || 'Cliente'}
                        </div>
                    </div>

                    {/* Códigos de Recarga - Centralizados */}
                    <div className="text-center mb-8">
                        <div className="text-lg font-bold mb-4">recargas</div>
                        {(() => {
                            // Agrupar códigos por tipo para combos
                            const tvCodes = codes.filter(c => c.package_type === 'tv');
                            const filmesCodes = codes.filter(c => c.package_type === 'filmes');

                            return (
                                <div className="space-y-2">
                                    {tvCodes.length > 0 && (
                                        <div>
                                            <div className="text-sm font-semibold">tv</div>
                                            {tvCodes.map((code, index) => (
                                                <div key={code.id?.$oid || code._id?.$oid || index} className="font-mono text-sm">
                                                    {code.code.replace(/(.{4})/g, '$1 ').trim()}
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                    {filmesCodes.length > 0 && (
                                        <div>
                                            <div className="text-sm font-semibold">filmes</div>
                                            {filmesCodes.map((code, index) => (
                                                <div key={code.id?.$oid || code._id?.$oid || index} className="font-mono text-sm">
                                                    {code.code.replace(/(.{4})/g, '$1 ').trim()}
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                    {/* Para pacotes simples (não combo) */}
                                    {tvCodes.length === 0 && filmesCodes.length === 0 && (
                                        <div>
                                            {codes.map((code, index) => (
                                                <div key={code.id?.$oid || code._id?.$oid || index} className="font-mono text-sm">
                                                    {code.code.replace(/(.{4})/g, '$1 ').trim()}
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            );
                        })()}
                    </div>

                    {/* Validade */}
                    <div className="text-center mb-8">
                        <div className="text-sm">
                            validade {selectedPackage?.validity_days || 30} dias após recarregar na tv
                        </div>
                    </div>

                    {/* Valor e Forma de Pagamento */}
                    <div className="text-center mb-8">
                        <div className="text-lg font-bold mb-2">
                            R$ {sale.total_sale_price.toFixed(2)}
                        </div>
                        <div className="text-sm">
                            {sale.payment_method}
                        </div>
                    </div>

                    {/* Vendedor - No final */}
                    <div className="absolute bottom-8 left-8">
                        <div className="text-sm">
                            vendedor: {selectedEmployee?.nome || 'N/A'}
                        </div>
                    </div>
                </div>
            </div>

            {/* Ações */}
            <div className="flex flex-col sm:flex-row gap-2 mt-4 w-full max-w-md no-print">
                <Button onClick={handleCopy} variant="outline" className="w-full">
                    <Copy className="mr-2 h-4 w-4"/> Copiar Texto
                </Button>
                <Button onClick={handlePrint} variant="outline" className="w-full">
                    <Printer className="mr-2 h-4 w-4"/> Imprimir
                </Button>
                <Button onClick={handleShare} className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                    <Share2 className="mr-2 h-4 w-4"/> Compartilhar / PDF
                </Button>
            </div>
            <Button onClick={onNewSale} className="w-full mt-2 max-w-md no-print">
                <ArrowLeft className="mr-2 h-4 w-4"/> Nova Venda
            </Button>
        </div>
    );
};

export default RechargeReceipt; 

