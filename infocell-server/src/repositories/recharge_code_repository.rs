use crate::{
    error::{AppError, Result},
    models::RechargeCode,
};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use futures_util::stream::TryStreamExt;
use mongodb::{Collection, Database, ClientSession};

#[derive(Clone)]
pub struct RechargeCodeRepository {
    collection: Collection<RechargeCode>,
}

impl RechargeCodeRepository {
    pub fn new(database: &Database) -> Self {
        Self {
            collection: database.collection("recharge_codes"),
        }
    }

    pub async fn insert_many(&self, codes: Vec<RechargeCode>) -> Result<()> {
        if codes.is_empty() {
            return Ok(());
        }
        self.collection
            .insert_many(codes)
            .await
            .map_err(AppError::DatabaseError)?;
        Ok(())
    }

    /// Conta quantos códigos estão disponíveis para um tipo de pacote específico
    pub async fn count_available(&self, package_type: &str) -> Result<u32> {
        let filter = doc! {
            "package_type": package_type,
            "is_available": true,
        };

        let count = self.collection.count_documents(filter).await?;
        Ok(count as u32)
    }

    pub async fn find_all(
        &self,
        page: u32,
        per_page: u32,
        package_type: Option<String>,
        is_available: Option<bool>,
    ) -> Result<(Vec<RechargeCode>, u64)> {
        let mut filter = doc! {};

        if let Some(pt) = package_type {
            filter.insert("package_type", pt);
        }
        if let Some(avail) = is_available {
            filter.insert("is_available", avail);
        }

        let skip = ((page - 1) * per_page) as u64;

        let total = self.collection.count_documents(filter.clone()).await?;

        let find_options = mongodb::options::FindOptions::builder()
            .skip(skip)
            .limit(per_page as i64)
            .sort(doc! { "imported_at": -1 }) // Ordenar pelos mais recentes
            .build();

        let mut cursor = self.collection.find(filter).with_options(find_options).await?;
        let mut codes = Vec::new();
        while let Some(code) = cursor.try_next().await? {
            codes.push(code);
        }

        Ok((codes, total))
    }

    pub async fn find_available_by_type(
        &self,
        package_type: &str,
        limit: u32,
    ) -> Result<Vec<RechargeCode>> {
        let filter = doc! {
            "package_type": package_type,
            "is_available": true,
        };
        let find_options = mongodb::options::FindOptions::builder()
            .limit(limit as i64)
            .build();
        let mut cursor = self.collection.find(filter).with_options(find_options).await?;
        let mut codes = Vec::new();
        while let Some(code) = cursor.try_next().await? {
            codes.push(code);
        }
        Ok(codes)
    }

    pub async fn mark_many_as_sold(
        &self,
        code_ids: &[ObjectId],
        sale_id: &ObjectId,
    ) -> Result<u64> {
        let filter = doc! { "_id": { "$in": code_ids } };
        let update = doc! {
            "$set": {
                "is_available": false,
                "sale_id": sale_id,
                "sold_at": Utc::now(),
            }
        };

        let result = self
            .collection
            .update_many(filter, update)
            .await?;
        
        Ok(result.matched_count)
    }

    pub async fn find_by_id(&self, id: &ObjectId) -> Result<Option<RechargeCode>> {
        self.collection
            .find_one(doc! { "_id": id })
            .await
            .map_err(AppError::DatabaseError)
    }

    /// Conta quantos códigos de recarga existem para um determinado tipo de pacote
    ///
    /// # Argumentos
    /// * `package_type` - O tipo de pacote para contar os códigos
    ///
    /// # Retorna
    /// * `Ok(u64)` - O número de códigos encontrados para o tipo de pacote
    /// * `Err(AppError)` - Se ocorreu um erro na operação
    pub async fn count_by_package_type(&self, package_type: &str) -> Result<u64> {
        let filter = doc! { "package_type": package_type };
        self.collection
            .count_documents(filter)
            .await
            .map_err(AppError::DatabaseError)
    }

    /// Encontra códigos que já existem no banco de dados a partir de uma lista fornecida.
    /// Esta função é otimizada para verificar duplicatas em lote antes da importação.
    ///
    /// # Argumentos
    /// * `codes_to_check` - Lista de códigos para verificar se já existem no banco
    ///
    /// # Retorna
    /// * `Ok(Vec<String>)` - Lista de códigos que já existem no banco de dados
    /// * `Err(AppError)` - Se ocorreu um erro na operação
    pub async fn find_existing_codes(&self, codes_to_check: &[String]) -> Result<Vec<String>> {
        if codes_to_check.is_empty() {
            return Ok(Vec::new());
        }

        let filter = doc! { "code": { "$in": codes_to_check } };

        // Projeção para retornar apenas o campo 'code'
        let find_options = mongodb::options::FindOptions::builder()
            .projection(doc! { "code": 1, "_id": 0 })
            .build();

        let mut cursor = self.collection.find(filter).with_options(find_options).await?;
        let mut existing_codes = Vec::new();

        while let Some(result) = cursor.try_next().await? {
            // Extrair o código do documento retornado
            existing_codes.push(result.code);
        }

        Ok(existing_codes)
    }

    /// Encontra um código disponível do tipo especificado e o marca como vendido atomicamente.
    /// Esta operação é thread-safe e previne condições de corrida.
    ///
    /// # Argumentos
    /// * `package_type` - O tipo de pacote do código a ser encontrado
    /// * `sale_id` - O ID da venda para associar ao código
    /// * `session` - Sessão opcional para operações transacionais
    ///
    /// # Retorna
    /// * `Ok(Some(RechargeCode))` - Se um código foi encontrado e marcado como vendido
    /// * `Ok(None)` - Se não há códigos disponíveis do tipo especificado
    /// * `Err(AppError)` - Se ocorreu um erro na operação
    pub async fn find_and_mark_one_as_sold(
        &self,
        package_type: &str,
        sale_id: &ObjectId,
        _session: Option<&mut ClientSession>, // Ignorado para compatibilidade
    ) -> Result<Option<RechargeCode>> {
        let filter = doc! {
            "package_type": package_type,
            "is_available": true,
        };

        let update = doc! {
            "$set": {
                "is_available": false,
                "sale_id": sale_id,
                "sold_at": Utc::now(),
            }
        };

        // find_one_and_update é uma operação atômica que encontra um documento
        // que corresponde ao filtro e o atualiza, retornando o documento original
        let options = mongodb::options::FindOneAndUpdateOptions::builder()
            .return_document(mongodb::options::ReturnDocument::Before)
            .build();

        let action = self.collection
            .find_one_and_update(filter, update)
            .with_options(options);

        // Ignorar session para compatibilidade com deployments que não suportam retryable writes
        let result = action.await;

        match result {
            Ok(Some(mut code)) => {
                // Atualizar o código retornado com os novos valores para refletir o estado atual
                code.is_available = false;
                code.sale_id = Some(*sale_id);
                code.sold_at = Some(Utc::now());
                Ok(Some(code))
            }
            Ok(None) => Ok(None), // Nenhum código disponível encontrado
            Err(e) => Err(AppError::DatabaseError(e)),
        }
    }

    /// Reverte um código para o estado 'disponível', removendo a referência da venda.
    /// Esta função é usada para reverter códigos reservados em caso de falha na venda.
    pub async fn unmark_as_sold(&self, code_id: &ObjectId) -> Result<()> {
        let filter = doc! { "_id": code_id };
        let update = doc! {
            "$set": { "is_available": true },
            "$unset": { "sale_id": "", "sold_at": "" }
        };
        self.collection.update_one(filter, update).await?;
        Ok(())
    }

    /// Atualiza uma lista de códigos com o ID da venda final.
    /// Esta função é usada para vincular os códigos reservados à venda após sua criação.
    pub async fn update_codes_with_sale_id(&self, code_ids: &[ObjectId], sale_id: &ObjectId) -> Result<()> {
        let filter = doc! { "_id": { "$in": code_ids } };
        let update = doc! { "$set": { "sale_id": sale_id } };
        self.collection.update_many(filter, update).await?;
        Ok(())
    }

    /// Busca códigos por ID da venda.
    /// Esta função é usada para obter os códigos vendidos em uma venda específica.
    pub async fn find_by_sale_id(&self, sale_id: &ObjectId) -> Result<Vec<RechargeCode>> {
        let filter = doc! { "sale_id": sale_id };
        let mut cursor = self.collection.find(filter).await?;
        let mut codes = Vec::new();
        while let Some(code) = cursor.try_next().await? {
            codes.push(code);
        }
        Ok(codes)
    }
}